<script setup lang="ts">
import MobileAppLayout from '@/layouts/MobileAppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Store {
    id: number;
    slug: string;
    name: string;
}

interface CalendarData {
    date: string;
    day: string;
    url: string;
    total: number;
    bebas: number;
    diambil: number;
    terkirim: number;
    selesai: number;
    batal: number;
    overtime: number;
    bbro: number;
}

interface User {
    id: number;
    role_id: number;
    email: string;
    name: string;
}

interface Props {
    store_slug: string;
    stores: Store[];
    month_now: string;
    month_prev: string;
    month_next: string;
    date_now: string;
    date_prev: string;
    date_next: string;
    data: CalendarData[];
    sum_total_job: number;
    sum_total_bebas: number;
    sum_total_diambil: number;
    sum_total_terkirim: number;
    sum_total_selesai: number;
    sum_total_batal: number;
    sum_total_overtime: number;
    sum_total_bbro: number;
    user: User;
}

const props = defineProps<Props>();

// Computed properties for role-based visibility
const isAdminRole = computed(() => [1, 2, 3, 4, 6].includes(props.user.role_id));
const isDriverRole = computed(() => props.user.role_id === 5);
const gridCols = computed(() => (isDriverRole.value ? 'grid-cols-6' : 'grid-cols-8'));

// Navigation handlers
const handleStoreChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    const url = new URL(window.location.href);
    url.searchParams.set('store', target.value);
    router.visit(url.toString());
};

const handleDateChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const url = new URL(window.location.href);
    url.searchParams.set('date', target.value);
    router.visit(url.toString());
};

const handleMonthChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const url = new URL(window.location.href);
    url.searchParams.set('month', target.value);
    router.visit(url.toString());
};

// Check if current date should be highlighted
const isToday = (dateStr: string) => {
    const today = new Date();
    const currentMonth = props.month_now;
    const currentDay = String(today.getDate()).padStart(2, '0');
    return currentMonth === today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') && dateStr === currentDay;
};
</script>

<template>
    <Head title="Calendar" />

    <MobileAppLayout :store-slug="store_slug">
        <!-- Header Section -->
        <template #header>
            <h2 class="text-lg font-bold">
                Calendar
                <select class="ml-1 inline border-none bg-transparent p-0 text-lg font-bold text-white" @change="handleStoreChange">
                    <option v-if="isAdminRole" :value="'all'" :selected="store_slug === 'all'">All</option>
                    <option v-for="store in stores" :key="store.id" :value="store.slug" :selected="store.slug === store_slug">
                        {{ store.name }}
                    </option>
                </select>
            </h2>
        </template>

        <!-- Navigation Section -->
        <template #subheader>
            <div class="mt-2 flex h-10 items-center justify-center text-red-500">
                <template v-if="store_slug === 'all'">
                    <Link :href="route('calendar', { date: date_prev, store: store_slug })" class="h-6 w-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </Link>
                    <input
                        type="date"
                        class="mx-3 w-56 border-none p-0 text-center text-lg font-bold focus:ring-0"
                        :max="date_next"
                        :value="date_now"
                        @change="handleDateChange"
                    />
                    <Link v-if="date_next" :href="route('calendar', { date: date_next, store: store_slug })" class="h-6 w-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </Link>
                </template>
                <template v-else>
                    <Link :href="route('calendar', { month: month_prev, store: store_slug })" class="h-6 w-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </Link>
                    <input
                        type="month"
                        class="mx-3 w-56 border-none p-0 text-center text-lg font-bold focus:ring-0"
                        :max="new Date().toISOString().slice(0, 7)"
                        :value="month_now"
                        @change="handleMonthChange"
                    />
                    <Link v-if="month_next" :href="route('calendar', { month: month_next, store: store_slug })" class="h-6 w-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </Link>
                </template>
            </div>

            <!-- Column Headers -->
            <div :class="['grid gap-0 text-center text-xs font-bold', gridCols]">
                <span class="grid-cols-2 py-2 text-gray-500 capitalize">
                    {{ store_slug === 'all' ? 'Toko' : 'Date' }}
                </span>
                <span class="grid-cols-1 py-2 text-black capitalize">Total</span>
                <span v-if="!isDriverRole" class="grid-cols-1 py-2 text-yellow-500 capitalize">Bebas</span>
                <span class="py-2 text-blue-500 capitalize">Diambil</span>
                <span class="py-2 text-blue-700 capitalize">Terkirim</span>
                <span class="py-2 text-green-500 capitalize">Selesai</span>
                <span v-if="isDriverRole" class="py-2 text-pink-500 capitalize">B-Bro</span>
                <template v-else>
                    <span class="py-2 text-red-500 capitalize">Overtime</span>
                    <span class="py-2 text-red-700 capitalize">Batal</span>
                </template>
            </div>

            <!-- Summary Row -->
            <div :class="['grid gap-0 text-center text-xs font-bold', gridCols, isDriverRole ? '' : 'bg-red-50']">
                <span class="grid-cols-2 py-2 text-gray-500 capitalize">TOTAL</span>
                <span class="grid-cols-1 py-2 text-black capitalize">{{ sum_total_job }}</span>
                <span v-if="!isDriverRole" class="grid-cols-1 py-2 text-yellow-500 capitalize">{{ sum_total_bebas }}</span>
                <span class="py-2 text-blue-500 capitalize">{{ sum_total_diambil }}</span>
                <span class="py-2 text-blue-700 capitalize">{{ sum_total_terkirim }}</span>
                <span class="py-2 text-green-500 capitalize">{{ sum_total_selesai }}</span>
                <span v-if="isDriverRole" class="py-2 text-pink-500 capitalize">{{ sum_total_bbro }}</span>
                <template v-else>
                    <span class="py-2 text-red-500 capitalize">
                        {{ sum_total_overtime }}
                        <template v-if="store_slug !== 'all'">
                            ~{{ sum_total_job > 0 ? Math.round((sum_total_overtime / sum_total_job) * 100) : 0 }}%
                        </template>
                    </span>
                    <span class="py-2 text-red-700 capitalize">{{ sum_total_batal }}</span>
                </template>
            </div>
        </template>

        <!-- Main Content with proper mobile spacing -->
        <!-- Calendar Data -->
        <div class="pt-44 pb-20">
            <template v-if="store_slug === 'all'">
                <!-- Store view - not implemented in dummy data -->
                <div class="py-8 text-center text-gray-500">Store view would show data grouped by stores</div>
            </template>
            <template v-else>
                <Link
                    v-for="(item, index) in data"
                    :key="index"
                    :href="item.url"
                    :class="[
                        'grid gap-0 border-b border-solid border-gray-200 text-center text-sm font-bold',
                        gridCols,
                        isToday(item.date) ? 'bg-yellow-100' : '',
                        index === 0 && isDriverRole ? 'bg-yellow-100' : '',
                    ]"
                >
                    <span class="flex h-11 grid-cols-2 flex-col items-center justify-center leading-none text-gray-500">
                        {{ item.date }}
                        <span class="mt-0 text-xs font-light">{{ item.day }}</span>
                    </span>
                    <span class="flex grid-cols-1 items-center justify-center text-black">{{ item.total }}</span>
                    <span v-if="!isDriverRole" class="flex grid-cols-1 items-center justify-center text-yellow-500">{{ item.bebas }}</span>
                    <span class="flex grid-cols-1 items-center justify-center text-blue-500">{{ item.diambil }}</span>
                    <span class="flex grid-cols-1 items-center justify-center text-blue-700">{{ item.terkirim }}</span>
                    <span class="flex grid-cols-1 items-center justify-center text-green-500">{{ item.selesai }}</span>
                    <span v-if="isDriverRole" class="flex grid-cols-1 items-center justify-center text-pink-500">
                        {{ item.bbro }}
                        <small class="opacity-50">/15</small>
                    </span>
                    <template v-else>
                        <span class="flex grid-cols-1 items-center justify-center text-red-500">{{ item.overtime }}</span>
                        <span class="flex grid-cols-1 items-center justify-center text-red-700">{{ item.batal }}</span>
                    </template>
                </Link>
            </template>
        </div>
    </MobileAppLayout>
</template>
